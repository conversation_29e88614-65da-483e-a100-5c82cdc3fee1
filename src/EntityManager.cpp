#include "EntityManager.h"

/**
 * Constructor - initializes the entity manager with no entities selected
 */
EntityManager::EntityManager() : selectedEntityIndex(-1)
{
    // Reserve some space in the vector for efficiency
    entities.reserve(10);  // Reserve space for up to 10 entities
}

/**
 * Destructor - cleanup if needed
 */
EntityManager::~EntityManager()
{
    // std::vector automatically cleans up its contents
    // Entity destructor will be called for each entity
}

/**
 * Add an existing entity to the manager
 */
void EntityManager::AddEntity(const Entity& entity)
{
    entities.push_back(entity);
}

/**
 * Create and add a new entity with the given parameters
 */
void EntityManager::AddEntity(float x, float y, float size, Color color, float speed)
{
    entities.emplace_back(x, y, size, color, speed);
}

/**
 * Select an entity by index
 */
void EntityManager::SelectEntity(int index)
{
    // First, deselect all entities
    DeselectAll();
    
    // Check if the index is valid
    if (index >= 0 && index < static_cast<int>(entities.size())) {
        selectedEntityIndex = index;
        entities[index].SetSelected(true);
    }
}

/**
 * Select entity based on mouse click
 */
void EntityManager::SelectEntityByMouse()
{
    // Only process if mouse button was just pressed (not held)
    if (IsMouseButtonPressed(MOUSE_BUTTON_LEFT)) {
        Vector2 mousePos = GetMousePosition();
        
        // Check each entity to see if mouse is over it
        for (int i = 0; i < static_cast<int>(entities.size()); i++) {
            if (entities[i].IsPointInside(mousePos.x, mousePos.y)) {
                SelectEntity(i);
                return;  // Exit after selecting the first entity found
            }
        }
        
        // If we get here, no entity was clicked - deselect all
        DeselectAll();
    }
}

/**
 * Select entity by keyboard number (1 for first entity, 2 for second, etc.)
 */
void EntityManager::SelectEntityByKeyboard(int keyNumber)
{
    int entityIndex = keyNumber - 1;  // Convert 1-based to 0-based index
    
    if (entityIndex >= 0 && entityIndex < static_cast<int>(entities.size())) {
        SelectEntity(entityIndex);
    }
}

/**
 * Deselect all entities
 */
void EntityManager::DeselectAll()
{
    selectedEntityIndex = -1;
    
    // Set all entities to not selected
    for (Entity& entity : entities) {
        entity.SetSelected(false);
    }
}

/**
 * Handle all input for entity selection and movement
 */
void EntityManager::HandleInput()
{
    // Handle mouse selection
    SelectEntityByMouse();
    
    // Handle keyboard selection
    if (IsKeyPressed(KEY_ONE)) {
        SelectEntityByKeyboard(1);
    }
    if (IsKeyPressed(KEY_TWO)) {
        SelectEntityByKeyboard(2);
    }
    
    // Handle movement for selected entity
    if (HasSelectedEntity()) {
        Entity* selectedEntity = GetSelectedEntity();
        
        // WASD movement
        if (IsKeyDown(KEY_W) || IsKeyDown(KEY_UP)) {
            selectedEntity->MoveUp();
        }
        if (IsKeyDown(KEY_S) || IsKeyDown(KEY_DOWN)) {
            selectedEntity->MoveDown();
        }
        if (IsKeyDown(KEY_A) || IsKeyDown(KEY_LEFT)) {
            selectedEntity->MoveLeft();
        }
        if (IsKeyDown(KEY_D) || IsKeyDown(KEY_RIGHT)) {
            selectedEntity->MoveRight();
        }
    }
}

/**
 * Update all entities
 */
void EntityManager::Update()
{
    for (Entity& entity : entities) {
        entity.Update();
    }
}

/**
 * Draw all entities
 */
void EntityManager::DrawAll()
{
    for (Entity& entity : entities) {
        entity.Draw();
    }
}

/**
 * Utility methods
 */
int EntityManager::GetSelectedEntityIndex() const
{
    return selectedEntityIndex;
}

bool EntityManager::HasSelectedEntity() const
{
    return selectedEntityIndex >= 0 && selectedEntityIndex < static_cast<int>(entities.size());
}

Entity* EntityManager::GetSelectedEntity()
{
    if (HasSelectedEntity()) {
        return &entities[selectedEntityIndex];
    }
    return nullptr;
}

size_t EntityManager::GetEntityCount() const
{
    return entities.size();
}

/**
 * Keep all entities within screen boundaries
 */
void EntityManager::ClampAllToBounds(int screenWidth, int screenHeight)
{
    for (Entity& entity : entities) {
        entity.ClampToBounds(screenWidth, screenHeight);
    }
}
