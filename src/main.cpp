#include "raylib.h"
#include "Entity.h"        // Include our Entity class
#include "EntityManager.h" // Include our EntityManager class

int main() {
    // Window configuration
    const int screenWidth = 1000;
    const int screenHeight = 600;

    // Initialize the window
    InitWindow(screenWidth, screenHeight, "My First Raylib C++ Application!!!");

    // Set target FPS (frames per second)
    SetTargetFPS(60);  // 60 FPS for smooth movement and input

    // Create our entity manager and add two entities
    EntityManager entityManager;

    // Add first entity (blue square) - positioned on the left side
    entityManager.AddEntity(screenWidth/4 - 25, screenHeight/2 - 25, 50, BLUE, 3.0f);

    // Add second entity (red square) - positioned on the right side
    entityManager.AddEntity(3*screenWidth/4 - 25, screenHeight/2 - 25, 50, RED, 3.0f);

    // Main game loop
    while (!WindowShouldClose()) {    // Detect window close button or ESC key
        // Update
        // Handle all input (selection and movement) through the entity manager
        entityManager.HandleInput();

        // Keep all entities within screen boundaries
        entityManager.ClampAllToBounds(screenWidth, screenHeight);

        // Update all entities
        entityManager.Update();

        // Draw
        BeginDrawing();

            // Clear background with a nice color
            ClearBackground(RAYWHITE);

            // Draw all entities
            entityManager.DrawAll();

            // Draw instructions for the player
            DrawText("Entity Selection & Movement System", 10, 10, 20, DARKGRAY);
            DrawText("Mouse: Click on an entity to select it", 10, 35, 16, GRAY);
            DrawText("Keyboard: Press '1' or '2' to select entity 1 or 2", 10, 55, 16, GRAY);
            DrawText("Movement: Use WASD or Arrow Keys to move selected entity", 10, 75, 16, GRAY);
            DrawText("Selected entities are highlighted with yellow outline", 10, 95, 16, GRAY);

            // Get mouse position for display
            Vector2 mousePos = GetMousePosition();

            // Display mouse coordinates and selection info
            DrawText(TextFormat("Mouse: X=%.0f, Y=%.0f", mousePos.x, mousePos.y), 10, 120, 16, DARKBLUE);

            // Show which entity is selected
            if (entityManager.HasSelectedEntity()) {
                DrawText(TextFormat("Selected Entity: %d", entityManager.GetSelectedEntityIndex() + 1), 10, 140, 16, GREEN);
            } else {
                DrawText("No entity selected", 10, 140, 16, RED);
            }

            // Draw FPS counter
            DrawFPS(10, screenHeight - 25);

        EndDrawing();
    }

    // Close window and OpenGL context
    CloseWindow();

    return 0;
}
