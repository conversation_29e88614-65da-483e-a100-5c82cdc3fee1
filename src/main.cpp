#include "raylib.h"
#include "Entity.h"        // Include our Entity class
#include "EntityManager.h" // Include our EntityManager class

int main() {
    // Window configuration
    const int screenWidth = 1000;
    const int screenHeight = 600;

    // Initialize the window
    InitWindow(screenWidth, screenHeight, "My First Raylib C++ Application!!!");

    // Set target FPS (frames per second)
    SetTargetFPS(60);  // 60 FPS for smooth movement and input

    // Camera setup for zoom functionality
    // Camera2D is Raylib's 2D camera system that handles transformations
    Camera2D camera = { 0 };
    camera.target = (Vector2){ screenWidth/2.0f, screenHeight/2.0f };  // What the camera is looking at
    camera.offset = (Vector2){ screenWidth/2.0f, screenHeight/2.0f };  // Camera offset (displacement from target)
    camera.rotation = 0.0f;    // Camera rotation in degrees
    camera.zoom = 1.0f;        // Camera zoom (1.0f = normal size, 2.0f = 2x zoom, 0.5f = half size)

    // Zoom configuration
    const float zoomSpeed = 0.1f;      // How much to zoom per input
    const float minZoom = 0.2f;        // Minimum zoom level (zoomed out)
    const float maxZoom = 5.0f;        // Maximum zoom level (zoomed in)

    // Create our entity manager and add two entities
    EntityManager entityManager;

    // Add first entity (blue square) - positioned on the left side
    entityManager.AddEntity(screenWidth/4 - 25, screenHeight/2 - 25, 50, BLUE, 3.0f);

    // Add second entity (red square) - positioned on the right side
    entityManager.AddEntity(3*screenWidth/4 - 25, screenHeight/2 - 25, 50, RED, 3.0f);

    // Main game loop
    while (!WindowShouldClose()) {    // Detect window close button or ESC key
        // Update

        // === ZOOM INPUT HANDLING ===
        // Handle mouse wheel zoom
        float wheel = GetMouseWheelMove();  // Returns positive for wheel up, negative for wheel down
        if (wheel != 0) {
            // Get mouse position in world coordinates before zoom
            Vector2 mouseWorldPos = GetScreenToWorld2D(GetMousePosition(), camera);

            // Update zoom level
            camera.zoom += (wheel * zoomSpeed);

            // Clamp zoom to our defined limits
            if (camera.zoom > maxZoom) camera.zoom = maxZoom;
            if (camera.zoom < minZoom) camera.zoom = minZoom;

            // Update camera target to zoom towards mouse position
            camera.target = mouseWorldPos;
        }

        // Handle keyboard zoom shortcuts
        if (IsKeyPressed(KEY_EQUAL) || IsKeyPressed(KEY_KP_ADD)) {  // '+' key (both regular and numpad)
            camera.zoom += zoomSpeed;
            if (camera.zoom > maxZoom) camera.zoom = maxZoom;
        }
        if (IsKeyPressed(KEY_MINUS) || IsKeyPressed(KEY_KP_SUBTRACT)) {  // '-' key (both regular and numpad)
            camera.zoom -= zoomSpeed;
            if (camera.zoom < minZoom) camera.zoom = minZoom;
        }

        // Handle all input (selection and movement) through the entity manager
        entityManager.HandleInput();

        // Keep all entities within screen boundaries
        entityManager.ClampAllToBounds(screenWidth, screenHeight);

        // Update all entities
        entityManager.Update();

        // Draw
        BeginDrawing();

            // Clear background with a nice color
            ClearBackground(RAYWHITE);

            // === CAMERA-BASED DRAWING ===
            // Begin 2D mode with camera (this applies zoom and transformations)
            BeginMode2D(camera);

                // Draw all entities (these will be affected by camera zoom/position)
                entityManager.DrawAll();

            // End 2D mode
            EndMode2D();

            // === UI DRAWING (NOT AFFECTED BY CAMERA) ===
            // UI elements are drawn after EndMode2D() so they stay fixed on screen

            // Draw instructions for the player
            DrawText("Entity Selection & Movement System", 10, 10, 20, DARKGRAY);
            DrawText("Mouse: Click on an entity to select it", 10, 35, 16, GRAY);
            DrawText("Keyboard: Press '1' or '2' to select entity 1 or 2", 10, 55, 16, GRAY);
            DrawText("Movement: Use WASD or Arrow Keys to move selected entity", 10, 75, 16, GRAY);
            DrawText("Selected entities are highlighted with yellow outline", 10, 95, 16, GRAY);

            // Draw zoom instructions
            DrawText("ZOOM CONTROLS:", 10, 125, 16, DARKBLUE);
            DrawText("Mouse Wheel: Zoom in/out towards cursor", 10, 145, 14, BLUE);
            DrawText("+ / - Keys: Zoom in/out", 10, 165, 14, BLUE);

            // Get mouse position for display
            Vector2 mousePos = GetMousePosition();
            Vector2 worldMousePos = GetScreenToWorld2D(mousePos, camera);  // Mouse position in world coordinates

            // Display mouse coordinates and zoom info
            DrawText(TextFormat("Screen Mouse: X=%.0f, Y=%.0f", mousePos.x, mousePos.y), 10, 190, 14, DARKBLUE);
            DrawText(TextFormat("World Mouse: X=%.0f, Y=%.0f", worldMousePos.x, worldMousePos.y), 10, 210, 14, DARKBLUE);
            DrawText(TextFormat("Zoom Level: %.2fx", camera.zoom), 10, 230, 16, PURPLE);

            // Show which entity is selected
            if (entityManager.HasSelectedEntity()) {
                DrawText(TextFormat("Selected Entity: %d", entityManager.GetSelectedEntityIndex() + 1), 10, 255, 16, GREEN);
            } else {
                DrawText("No entity selected", 10, 255, 16, RED);
            }

            // Draw FPS counter
            DrawFPS(10, screenHeight - 25);

        EndDrawing();
    }

    // Close window and OpenGL context
    CloseWindow();

    return 0;
}
