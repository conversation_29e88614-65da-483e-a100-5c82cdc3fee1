{
    // C++ Configuration
    "C_Cpp.default.cppStandard": "c++17",
    "C_Cpp.default.cStandard": "c11",
    "C_Cpp.default.intelliSenseMode": "linux-gcc-x64",
    "C_Cpp.default.compilerPath": "/usr/bin/g++",
    "C_Cpp.default.includePath": [
        "${workspaceFolder}/include",
        "${workspaceFolder}/external/raylib/src",
        "${workspaceFolder}/src"
    ],
    "C_Cpp.default.defines": [
        "PLATFORM_DESKTOP"
    ],
    "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools",
    
    // CMake Configuration
    "cmake.buildDirectory": "${workspaceFolder}/build",
    "cmake.generator": "Unix Makefiles",
    "cmake.configureOnOpen": true,
    "cmake.buildBeforeRun": true,
    "cmake.clearOutputBeforeBuild": true,
    
    // Code Formatting
    "editor.formatOnSave": true,
    "editor.formatOnType": false,
    "C_Cpp.formatting": "clangFormat",
    "C_Cpp.clang_format_style": "{ BasedOnStyle: Google, IndentWidth: 4, ColumnLimit: 100, AllowShortFunctionsOnASingleLine: Empty }",
    
    // File Associations
    "files.associations": {
        "*.h": "cpp",
        "*.hpp": "cpp",
        "*.cpp": "cpp",
        "*.c": "c",
        "CMakeLists.txt": "cmake",
        "*.cmake": "cmake"
    },
    
    // Editor Settings for C++
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.rulers": [100],
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 100,
    
    // IntelliSense Settings
    "C_Cpp.intelliSenseEngine": "default",
    "C_Cpp.errorSquiggles": "enabled",
    "C_Cpp.autocomplete": "default",
    "C_Cpp.suggestSnippets": true,
    
    // Git Settings
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,
    
    // Terminal Settings
    "terminal.integrated.cwd": "${workspaceFolder}",
    
    // Exclude build artifacts from search
    "search.exclude": {
        "**/build/**": true,
        "**/.git/**": true,
        "**/node_modules/**": true
    },
    
    // File Watcher Exclusions
    "files.watcherExclude": {
        "**/build/**": true,
        "**/.git/objects/**": true,
        "**/.git/subtree-cache/**": true
    }
}
